[package]
name = "ocr-grammar-assistant"
version = "0.1.0"
description = "OCR & Grammar Assistant - Modern text recognition desktop application"
authors = ["OCR Assistant Team"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.1", features = ["codegen"] }

[dependencies]
# Core Tauri dependencies
tauri = { version = "2.7.0", features = [] }
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
tauri-plugin-shell = "2"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime
tokio = { version = "1.47.1", features = ["full"] }

# Harper grammar checker - native Rust implementation
harper-core = "0.56.0"

# Regular expressions for advanced grammar patterns
regex = "1.10"

# HTTP client for OCR service
reqwest = { version = "0.12", features = ["json", "http2", "gzip", "brotli", "deflate"] }

# CSV handling
csv = "1.3"

# Error handling
anyhow = "1.0"

# Logging
log = "0.4"

# Utilities
chrono = { version = "0.4", features = ["serde"] }
dashmap = "6.0"
