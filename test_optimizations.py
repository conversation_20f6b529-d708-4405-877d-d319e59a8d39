#!/usr/bin/env python3
"""
Test script to verify HTTP/2 and compression optimizations
"""

import asyncio
import aiohttp
import time
import json
import tempfile
import os
from pathlib import Path

async def test_http2_support():
    """Test if the server supports HTTP/2"""
    print("Testing HTTP/2 support...")
    
    connector = aiohttp.TCPConnector(force_close=True)
    timeout = aiohttp.ClientTimeout(total=30)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        try:
            async with session.get('http://127.0.0.1:8000/health') as response:
                print(f"Response status: {response.status}")
                print(f"HTTP version: {response.version}")
                print(f"Headers: {dict(response.headers)}")
                
                if response.version.major == 2:
                    print("✅ HTTP/2 is supported!")
                else:
                    print("⚠️  HTTP/2 not detected, using HTTP/1.1")
                    
                return response.version.major == 2
        except Exception as e:
            print(f"❌ Error testing HTTP/2: {e}")
            return False

async def test_compression():
    """Test response compression"""
    print("\nTesting response compression...")
    
    headers = {
        'Accept-Encoding': 'gzip, deflate, br'
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get('http://127.0.0.1:8000/metrics', headers=headers) as response:
                print(f"Response status: {response.status}")
                content_encoding = response.headers.get('Content-Encoding')
                content_length = response.headers.get('Content-Length')
                
                if content_encoding:
                    print(f"✅ Response is compressed with: {content_encoding}")
                else:
                    print("⚠️  Response is not compressed")
                
                if content_length:
                    print(f"Content length: {content_length} bytes")
                
                data = await response.json()
                print(f"Metrics data size: {len(json.dumps(data))} characters")
                
                return content_encoding is not None
        except Exception as e:
            print(f"❌ Error testing compression: {e}")
            return False

async def test_batch_processing():
    """Test batch processing endpoint"""
    print("\nTesting batch processing...")
    
    # Create some test files
    test_files = []
    temp_dir = tempfile.mkdtemp()
    
    try:
        for i in range(3):
            test_file = os.path.join(temp_dir, f"test_image_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"Test content for file {i}")
            test_files.append(test_file)
        
        batch_request = {
            "files": test_files,
            "preprocessing_options": {
                "enhance_contrast": True,
                "denoise": True,
                "threshold_method": "adaptive_gaussian",
                "apply_morphology": True
            },
            "max_file_size_mb": 1
        }
        
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.post('http://127.0.0.1:8000/ocr/batch', 
                                  json=batch_request) as response:
                end_time = time.time()
                
                print(f"Response status: {response.status}")
                print(f"Processing time: {end_time - start_time:.2f}s")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Batch processing successful!")
                    print(f"Files processed: {data.get('files_processed', 0)}")
                    print(f"Files failed: {data.get('files_failed', 0)}")
                    print(f"Total processing time: {data.get('total_processing_time', 0):.2f}s")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Batch processing failed: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Error testing batch processing: {e}")
        return False
    finally:
        # Cleanup test files
        for test_file in test_files:
            try:
                os.unlink(test_file)
            except:
                pass
        try:
            os.rmdir(temp_dir)
        except:
            pass

async def test_performance_metrics():
    """Test enhanced performance metrics"""
    print("\nTesting performance metrics...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get('http://127.0.0.1:8000/metrics') as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Check for new metrics
                    expected_metrics = [
                        'http2_requests', 'compressed_responses', 'batch_requests',
                        'batch_files_processed', 'http2_usage_rate', 'compression_rate',
                        'batch_efficiency', 'min_response_time', 'max_response_time'
                    ]
                    
                    found_metrics = []
                    for metric in expected_metrics:
                        if metric in data:
                            found_metrics.append(metric)
                            print(f"✅ {metric}: {data[metric]}")
                        else:
                            print(f"⚠️  {metric}: not found")
                    
                    print(f"\nFound {len(found_metrics)}/{len(expected_metrics)} expected metrics")
                    return len(found_metrics) > len(expected_metrics) // 2
                else:
                    print(f"❌ Failed to get metrics: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Error testing metrics: {e}")
            return False

async def main():
    """Run all tests"""
    print("🚀 Testing HTTP Communication Optimizations")
    print("=" * 50)
    
    # Wait for server to be ready
    print("Waiting for server to be ready...")
    await asyncio.sleep(2)
    
    results = {}
    
    # Run tests
    results['http2'] = await test_http2_support()
    results['compression'] = await test_compression()
    results['batch_processing'] = await test_batch_processing()
    results['metrics'] = await test_performance_metrics()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All optimizations are working correctly!")
    else:
        print("⚠️  Some optimizations may need attention.")

if __name__ == "__main__":
    asyncio.run(main())
